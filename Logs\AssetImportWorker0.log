Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14242 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-10T17:23:32Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Development/Unity/UnityProjects/AnimationGenerator
-logFile
Logs/AssetImportWorker0.log
-srvPort
8094
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Development/Unity/UnityProjects/AnimationGenerator
D:/Development/Unity/UnityProjects/AnimationGenerator
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [10352]  Target information:

Player connection [10352]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 4088117628 [EditorId] 4088117628 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10352]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 4088117628 [EditorId] 4088117628 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10352]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4088117628 [EditorId] 4088117628 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10352]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4088117628 [EditorId] 4088117628 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10352]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 4088117628 [EditorId] 4088117628 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10352]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 4088117628 [EditorId] 4088117628 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10352] Host joined multi-casting on [***********:54997]...
Player connection [10352] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 5.42 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Development/Unity/UnityProjects/AnimationGenerator/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon(TM) RX Vega 11 Graphics (ID=0x15d8)
    Vendor:          ATI
    VRAM:            7121 MB
    App VRAM Budget: 8439 MB
    Driver:          31.0.21921.1000
    Unified Memory Architecture
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56124
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.008875 seconds.
- Loaded All Assemblies, in  0.822 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 711 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.788 seconds
Domain Reload Profiling: 2605ms
	BeginReloadAssembly (299ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (97ms)
	LoadAllAssembliesAndSetupDomain (318ms)
		LoadAssemblies (288ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (310ms)
				TypeCache.ScanAssembly (285ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1789ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1622ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (937ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (201ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.600 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.84 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 47.802 seconds
Domain Reload Profiling: 53392ms
	BeginReloadAssembly (1939ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (288ms)
	RebuildNativeTypeToScriptingClass (66ms)
	initialDomainReloadingComplete (277ms)
	LoadAllAssembliesAndSetupDomain (3018ms)
		LoadAssemblies (3155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1557ms)
			TypeCache.Refresh (1145ms)
				TypeCache.ScanAssembly (656ms)
			BuildScriptInfoCaches (359ms)
			ResolveRequiredComponents (44ms)
	FinalizeReload (47804ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (47313ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (30465ms)
			ProcessInitializeOnLoadAttributes (16108ms)
			ProcessInitializeOnLoadMethodAttributes (672ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 7.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7836 unused Assets / (9.0 MB). Loaded Objects now: 8538.
Memory consumption went from 196.7 MB to 187.7 MB.
Total: 44.418900 ms (FindLiveObjects: 4.349400 ms CreateObjectMapping: 4.015500 ms MarkObjects: 26.492900 ms  DeleteObjects: 9.558100 ms)

========================================================================
Received Import Request.
  Time since last request: 171546.386989 seconds.
  path: Assets/Scripts/Game/Data/GeneratorConfig.cs
  artifactKey: Guid(f76ee412c4ca8224499288631ca9e59c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Game/Data/GeneratorConfig.cs using Guid(f76ee412c4ca8224499288631ca9e59c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e05954a2b196c561bfc00350a5ab473') in 0.0192282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

